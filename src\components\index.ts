/**
 * Component Library Index
 *
 * This file organizes exports from various component groups for easier importing
 */

// Widget Builder Component - Simplified single implementation
// SmartWidgetBuilder is the only widget builder implementation
export { default as WidgetBuilder } from './SmartWidgetBuilder';
export { default as SmartWidgetBuilder } from './SmartWidgetBuilder';

// Widget Builder Sub-components - use these for specific parts
export * from './widget-builder';