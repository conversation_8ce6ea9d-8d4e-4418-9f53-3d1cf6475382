# Widget Builder Components

This directory contains components for the widget builder system. The components are organized into several categories:

## Main Builder Component

The main widget builder component is `SmartWidgetBuilder.tsx` in the parent directory. It's now the only widget builder implementation, providing a clean, simplified approach with direct configuration and no modal complexity.

## Core Components

- **WidgetPreview.tsx**: Renders a preview of the widget based on the current configuration
- **DevicePreview.tsx**: Wraps content in mobile, tablet, or desktop preview frames
- **EmbedCodeGenerator.tsx**: Generates embed code for the widget
- **TemplatePresets.tsx**: Display and select from predefined widget templates
- **ImageWithFallback.tsx**: Image component with fallback support for broken images

## Form Fields

All form field components are in the `form-fields` directory and follow a consistent API:
- Each accepts `label`, `fieldName`, `control` props
- All have proper TypeScript types
- All use React Hook Form

## Upload Components

- **ImageUploader.tsx**: Comprehensive image upload component with preview, validation, and optimization. Use for both logos and general images.

## Tab Components

- **IntegrationsTab.tsx**: Integration with external services

## Usage

```tsx
// Import individual components
import {
  WidgetPreview,
  DevicePreview,
  ColorField,
  SliderField
} from '@/components/widget-builder';

// Import the main builder component
import { WidgetBuilder } from '@/components';
```

## Migration Notes

1. The original complex modal-based `SmartWidgetBuilder` has been completely redesigned.
2. All modal components and complex sub-components have been removed.
3. The new `SmartWidgetBuilder` provides a simplified, direct interface with inline configuration.
4. All features are now accessible without modal navigation.
5. The component is exported as both `WidgetBuilder` and `SmartWidgetBuilder` for compatibility.