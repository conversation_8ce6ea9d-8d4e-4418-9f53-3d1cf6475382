import axios from "axios";
import { toast } from "@/components/ui/use-toast";

// Configure axios defaults for CSRF handling
axios.defaults.withCredentials = true;
axios.defaults.withXSRFToken = true;

// Use environment variable for API URL
const apiUrl = import.meta.env.VITE_API_URL || '/api';

// Axios instance with baseURL from .env or default to /api
const api = axios.create({
  baseURL: apiUrl,
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json"
  },
  withCredentials: true, // Needed for Sanctum (cookie-based auth)
  withXSRFToken: true // Automatically include XSRF token from cookies
});

// Request interceptor for error handling
api.interceptors.request.use(
  (config) => {
    // Sanctum handles authentication cookies automatically
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    // Check if server returned HTML instead of JSON (likely a server error)
    if (
      typeof error?.response?.data === "string" &&
      error.response.data.includes("<!DOCTYPE")
    ) {
      toast({
        title: "Server Error",
        description: "The server returned an invalid response. Please contact support.",
        variant: "destructive"
      });
      return Promise.reject(new Error("Invalid HTML response received from the server."));
    }

    // Handle authentication errors (401)
    if (error.response?.status === 401) {
      // Don't redirect automatically if this is an auth check request
      const isAuthCheck = error.config?.url?.includes('/auth/check') || error.config?.url?.includes('/user');

      if (!isAuthCheck) {
        // For cleaner user experience, redirect to login page through router
        // but we use window.location as a fallback if needed
        if (typeof window !== 'undefined') {
          window.location.href = '/login';
        }

        toast({
          title: "Session Expired",
          description: "Your session has expired. Please log in again.",
          variant: "destructive"
        });
      }

      return Promise.reject(error);
    }

    // Handle other API errors
    const errorMessage =
      error.response?.data?.message ||
      error.response?.data?.error ||
      "An unexpected error occurred";

    // Show toast for all errors except validation errors (422)
    if (error.response?.status !== 422) {
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }

    return Promise.reject(error);
  }
);

export default api;
