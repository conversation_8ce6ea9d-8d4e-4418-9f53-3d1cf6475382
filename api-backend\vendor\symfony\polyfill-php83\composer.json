{"name": "symfony/polyfill-php83", "type": "library", "description": "Symfony polyfill backporting some PHP 8.3+ features to lower PHP versions", "keywords": ["polyfill", "shim", "compatibility", "portable"], "homepage": "https://symfony.com", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "require": {"php": ">=7.2"}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php83\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "minimum-stability": "dev", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}}