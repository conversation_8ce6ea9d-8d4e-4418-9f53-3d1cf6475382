"use client"

import { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Save,
  Eye,
  EyeOff,
  Monitor,
  Tablet,
  Smartphone as PhoneIcon,
  Power,
  Trash2,
  Palette,
  MessageSquare,
  Settings,
  ChevronDown,
  ChevronUp,
  FileText,
  Link,
  Shield,
  Smartphone,
  Brain,
  CheckCircle2,
  AlertTriangle
} from 'lucide-react';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { widgetService, Widget } from '@/utils/widgetService';
import { ChatWidgetPreview } from '../../widget-preview';

// Complete widget configuration schema - ALL FEATURES INCLUDED
const widgetSchema = z.object({
  // Basic Settings
  name: z.string().min(1, "Widget name is required").max(50, "Name too long"),
  welcomeMessage: z.string().min(1, "Welcome message is required").max(200, "Message too long"),
  botName: z.string().min(1, "Bot name is required").max(30, "Name too long").default("AI Assistant"),
  placeholderText: z.string().min(1, "Placeholder is required").max(100, "Text too long").default("Type your message..."),

  // Appearance & Theming
  primaryColor: z.string().regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, "Invalid color").default("#7E69AB"),
  secondaryColor: z.string().regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, "Invalid color").default("#ffffff"),
  position: z.enum(["bottom-right", "bottom-left", "top-right", "top-left"]).default("bottom-right"),
  presetTheme: z.string().default("modern"),
  builderTheme: z.enum(["light", "dark", "system"]).default("system"),
  animation: z.enum(["none", "fade", "slide", "bounce"]).default("fade"),
  shadow: z.enum(["none", "sm", "md", "lg", "xl"]).default("md"),

  // Logo & Branding
  logoUpload: z.boolean().default(false),
  logoUrl: z.string().default(''),
  logoFile: z.instanceof(File).optional().nullable(),

  // Typography
  fontFamily: z.string().default("Inter"),
  fontSize: z.enum(["sm", "md", "lg"]).default("md"),

  // Features
  preChat: z.boolean().default(false),
  postChat: z.boolean().default(false),
  webhooks: z.boolean().default(false),
  domainRestriction: z.boolean().default(false),
  conversationPersistence: z.boolean().default(false),
  mobileOptimization: z.boolean().default(true),
  customCSS: z.boolean().default(false),
  aiModelSelection: z.boolean().default(false),
  userRatings: z.boolean().default(true),

  // Pre-Chat Form Configuration
  preChatFields: z.array(z.object({
    type: z.enum(["text", "email", "phone", "select", "textarea", "checkbox"]),
    name: z.string(),
    label: z.string(),
    required: z.boolean(),
    options: z.array(z.string()).optional()
  })).default([]),
  preChatRequired: z.boolean().default(false),

  // Post-Chat Survey Configuration
  postChatQuestions: z.array(z.object({
    type: z.enum(["rating", "text", "select", "checkbox"]),
    question: z.string(),
    required: z.boolean(),
    options: z.array(z.string()).optional()
  })).default([]),

  // Webhook Configuration
  webhookUrl: z.string().url().optional().or(z.literal('')),
  webhookEvents: z.array(z.enum(["message_sent", "conversation_started", "conversation_ended", "rating_submitted"])).default([]),
  webhookSecret: z.string().default(''),

  // Domain Restriction
  allowedDomains: z.array(z.string()).default([]),

  // Custom CSS
  customCSSCode: z.string().default(''),

  // AI Model Configuration
  selectedAiModel: z.string().default("auto"),
  contextRetention: z.enum(["session", "persistent", "none"]).default("session"),
  maxMessagesStored: z.number().min(10).max(1000).default(100),

  // Behavior Settings
  autoOpen: z.boolean().default(false),
  autoOpenDelay: z.number().min(0).max(60).default(5),
  showTypingIndicator: z.boolean().default(true),
  enableUserRatings: z.boolean().default(true),
  startMinimized: z.boolean().default(false),
  closeAfterInactivity: z.boolean().default(false),
  inactivityTimeout: z.number().min(1).max(60).default(5),
  collectUserData: z.boolean().default(true),

  // Advanced Settings
  enableAnalytics: z.boolean().default(true),
  debugMode: z.boolean().default(false),
  loadTimeoutMs: z.number().min(1000).max(30000).default(5000),
  customParameters: z.record(z.string()).default({}),

  // Mobile Settings
  mobileBreakpoint: z.number().default(768),
  mobilePosition: z.enum(["bottom", "top", "full"]).default("bottom"),
  mobileAnimation: z.enum(["slide-up", "fade", "none"]).default("slide-up"),

  // Button Customization
  buttonText: z.string().default("Chat with us"),
  buttonSize: z.enum(["sm", "md", "lg"]).default("md"),
  buttonShape: z.enum(["round", "square", "pill"]).default("round"),
});

type WidgetFormValues = z.infer<typeof widgetSchema>;

interface SimpleWidgetBuilderProps {
  widgetId?: string;
  onSave?: (widget: Widget) => void;
  onCancel?: () => void;
}

/**
 * Simple Widget Builder - Direct, no-modal interface
 * Everything is accessible without clicking into modals
 * Designed for non-technical users with immediate feedback
 */
const SimpleWidgetBuilderContent = ({ widgetId, onSave, onCancel }: SimpleWidgetBuilderProps) => {
  const { toast } = useToast();

  // Simple UI State
  const [previewVisible, setPreviewVisible] = useState(true);
  const [previewDevice, setPreviewDevice] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');
  const [saving, setSaving] = useState(false);
  const [isActive, setIsActive] = useState(true);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deleting, setDeleting] = useState(false);

  // Collapsible sections state
  const [advancedOpen, setAdvancedOpen] = useState(false);
  const [featuresOpen, setFeaturesOpen] = useState(true);

  // Form setup with complete schema
  const form = useForm<WidgetFormValues>({
    resolver: zodResolver(widgetSchema),
    defaultValues: {
      // Basic Settings
      name: "My Chat Widget",
      welcomeMessage: "Hello! How can I help you today?",
      botName: "AI Assistant",
      placeholderText: "Type your message...",

      // Appearance & Theming
      primaryColor: "#7E69AB",
      secondaryColor: "#ffffff",
      position: "bottom-right",
      presetTheme: "modern",
      builderTheme: "system",
      animation: "fade",
      shadow: "md",

      // Logo & Branding
      logoUpload: false,
      logoUrl: "",
      logoFile: null,

      // Typography
      fontFamily: "Inter",
      fontSize: "md",

      // Features
      preChat: false,
      postChat: false,
      webhooks: false,
      domainRestriction: false,
      conversationPersistence: false,
      mobileOptimization: true,
      customCSS: false,
      aiModelSelection: false,
      userRatings: true,

      // Pre-Chat Form Configuration
      preChatFields: [],
      preChatRequired: false,

      // Post-Chat Survey Configuration
      postChatQuestions: [],

      // Webhook Configuration
      webhookUrl: "",
      webhookEvents: [],
      webhookSecret: "",

      // Domain Restriction
      allowedDomains: [],

      // Custom CSS
      customCSSCode: "",

      // AI Model Configuration
      selectedAiModel: "auto",
      contextRetention: "session",
      maxMessagesStored: 100,

      // Behavior Settings
      autoOpen: false,
      autoOpenDelay: 5,
      showTypingIndicator: true,
      enableUserRatings: true,
      startMinimized: false,
      closeAfterInactivity: false,
      inactivityTimeout: 5,
      collectUserData: true,

      // Advanced Settings
      enableAnalytics: true,
      debugMode: false,
      loadTimeoutMs: 5000,
      customParameters: {},

      // Mobile Settings
      mobileBreakpoint: 768,
      mobilePosition: "bottom",
      mobileAnimation: "slide-up",

      // Button Customization
      buttonText: "Chat with us",
      buttonSize: "md",
      buttonShape: "round",
    },
    mode: "onChange"
  });

  // Load existing widget data if editing
  useEffect(() => {
    if (widgetId) {
      loadWidgetData();
    }
  }, [widgetId]);

  const loadWidgetData = async () => {
    try {
      const response = await widgetService.getWidget(Number(widgetId));
      const widget = response.data;

      if (widget) {
        // TODO: Update transformer to handle new schema
        // const formValues = transformApiToForm(widget);
        // form.reset(formValues);
        setIsActive(widget.is_active);
      }
    } catch (error) {
      console.error('Error loading widget:', error);
      toast({
        title: "Error Loading Widget",
        description: "Could not load widget data. Starting with defaults.",
        variant: "destructive",
      });
    }
  };

  // Simplified save function
  const handleSave = async () => {
    setSaving(true);
    try {
      const result = await form.trigger();
      if (!result) {
        toast({
          title: "Validation Error",
          description: "Please fix the errors in the form before saving.",
          variant: "destructive",
        });
        setSaving(false);
        return;
      }

      const formData = form.getValues();
      // TODO: Update transformer to handle new schema
      // const widgetData = transformFormToApi(formData);
      const widgetData: any = { ...formData, is_active: isActive };

      let savedWidget: any;
      if (widgetId) {
        savedWidget = await widgetService.updateWidget(Number(widgetId), widgetData);
      } else {
        savedWidget = await widgetService.createWidget(widgetData);
      }

      if (savedWidget.data) {
        toast({
          title: "Widget Saved",
          description: widgetId ? "Widget updated successfully." : "New widget created successfully.",
          className: "bg-green-50 border-green-200 text-green-800",
        });
        onSave?.(savedWidget.data);
      }
    } catch (error) {
      console.error('Error saving widget:', error);
      toast({
        title: "Error Saving Widget",
        description: "There was a problem saving your widget. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // Simplified delete function
  const handleDelete = async () => {
    if (!widgetId) return;

    setDeleting(true);
    try {
      await widgetService.deleteWidget(Number(widgetId));
      toast({
        title: "Widget Deleted",
        description: "The widget has been deleted successfully.",
        className: "bg-green-50 border-green-200 text-green-800",
      });
      onCancel?.();
    } catch (error) {
      console.error('Error deleting widget:', error);
      toast({
        title: "Error Deleting Widget",
        description: "Failed to delete the widget. Please try again.",
        variant: "destructive",
      });
    } finally {
      setDeleting(false);
      setShowDeleteDialog(false);
    }
  };

  // Simple render - no template step, direct interface
  return (
    <div className="bg-background min-h-screen">
      {/* Header */}
      <div className="border-b border-border px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h2 className="text-lg font-semibold text-foreground">Widget Builder</h2>
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
              Simple & Direct
            </Badge>
          </div>

          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <Switch
                id="widget-active"
                checked={isActive}
                onCheckedChange={setIsActive}
              />
              <Label htmlFor="widget-active" className="text-sm font-medium cursor-pointer">
                {isActive ? (
                  <span className="text-green-600 flex items-center">
                    <Power className="w-3 h-3 mr-1" /> Active
                  </span>
                ) : (
                  <span className="text-muted-foreground flex items-center">
                    <Power className="w-3 h-3 mr-1" /> Inactive
                  </span>
                )}
              </Label>
            </div>

            {widgetId && (
              <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
                <AlertDialogTrigger asChild>
                  <Button variant="outline" size="sm" className="text-red-500 border-red-200 hover:bg-red-50">
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                    <AlertDialogDescription>
                      This action cannot be undone. This will permanently delete the widget.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleDelete}
                      className="bg-red-500 hover:bg-red-600"
                      disabled={deleting}
                    >
                      {deleting ? "Deleting..." : "Delete Widget"}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}

            <Button
              variant="outline"
              size="sm"
              onClick={() => setPreviewVisible(!previewVisible)}
            >
              {previewVisible ? <EyeOff className="w-4 h-4 mr-2" /> : <Eye className="w-4 h-4 mr-2" />}
              {previewVisible ? "Hide Preview" : "Show Preview"}
            </Button>

            <Button
              onClick={handleSave}
              disabled={saving}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Save className="w-4 h-4 mr-2" />
              {saving ? "Saving..." : "Save Widget"}
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex min-h-[800px]">
        {/* Preview Panel */}
        {previewVisible && (
          <div className="w-1/2 bg-background border-r border-border p-6">
            <div className="mb-4 flex items-center justify-between">
              <h3 className="text-lg font-medium text-foreground">Live Preview</h3>
              <div className="flex items-center space-x-1 bg-muted rounded-lg p-1">
                <Button
                  variant={previewDevice === "desktop" ? "secondary" : "ghost"}
                  size="sm"
                  className="h-8 px-2.5"
                  onClick={() => setPreviewDevice("desktop")}
                >
                  <Monitor className="h-4 w-4" />
                </Button>
                <Button
                  variant={previewDevice === "tablet" ? "secondary" : "ghost"}
                  size="sm"
                  className="h-8 px-2.5"
                  onClick={() => setPreviewDevice("tablet")}
                >
                  <Tablet className="h-4 w-4" />
                </Button>
                <Button
                  variant={previewDevice === "mobile" ? "secondary" : "ghost"}
                  size="sm"
                  className="h-8 px-2.5"
                  onClick={() => setPreviewDevice("mobile")}
                >
                  <PhoneIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="bg-muted rounded-lg p-4 min-h-[600px] flex items-center justify-center">
              <ChatWidgetPreview
                settings={{
                  primaryColor: form.watch('primaryColor'),
                  secondaryColor: form.watch('secondaryColor'),
                  position: form.watch('position'),
                  headerTitle: form.watch('botName'),
                  initialMessage: form.watch('welcomeMessage'),
                  inputPlaceholder: form.watch('placeholderText'),
                  fontFamily: form.watch('fontFamily'),
                  borderRadius: 8,
                  chatIconSize: 40,
                }}
                widgetId={widgetId || "preview_widget"}
              />
            </div>
          </div>
        )}

        {/* Settings Panel - Simplified Direct Interface */}
        <div className={`${previewVisible ? 'w-1/2' : 'w-full'} bg-muted/50 overflow-y-auto`}>
          <div className="p-6 space-y-6">
            <Form {...form}>
              {/* Basic Settings */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <MessageSquare className="w-5 h-5 mr-2 text-blue-600" />
                    Basic Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Widget Name</FormLabel>
                        <FormControl>
                          <Input placeholder="My Chat Widget" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="welcomeMessage"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Welcome Message</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Hello! How can I help you today?"
                            className="min-h-[80px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="botName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Bot Name</FormLabel>
                          <FormControl>
                            <Input placeholder="AI Assistant" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="placeholderText"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Input Placeholder</FormLabel>
                          <FormControl>
                            <Input placeholder="Type your message..." {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Appearance Templates */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <Palette className="w-5 h-5 mr-2 text-purple-600" />
                    Appearance Templates
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="presetTheme"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Choose a Template</FormLabel>
                        <FormControl>
                          <div className="grid grid-cols-2 gap-3">
                            {/* Modern Purple Template */}
                            <div
                              className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${field.value === 'modern' ? 'border-purple-500 bg-purple-50' : 'border-gray-200 hover:border-purple-300'
                                }`}
                              onClick={() => {
                                field.onChange('modern');
                                form.setValue('primaryColor', '#7E69AB');
                                form.setValue('secondaryColor', '#ffffff');
                              }}
                            >
                              <div className="flex items-center space-x-2 mb-2">
                                <div className="w-4 h-4 bg-purple-500 rounded"></div>
                                <div className="w-4 h-4 bg-white border rounded"></div>
                              </div>
                              <p className="font-medium text-sm">Modern Purple</p>
                              <p className="text-xs text-gray-500">Professional & clean</p>
                            </div>

                            {/* Ocean Blue Template */}
                            <div
                              className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${field.value === 'ocean' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-blue-300'
                                }`}
                              onClick={() => {
                                field.onChange('ocean');
                                form.setValue('primaryColor', '#3B82F6');
                                form.setValue('secondaryColor', '#EFF6FF');
                              }}
                            >
                              <div className="flex items-center space-x-2 mb-2">
                                <div className="w-4 h-4 bg-blue-500 rounded"></div>
                                <div className="w-4 h-4 bg-blue-50 border rounded"></div>
                              </div>
                              <p className="font-medium text-sm">Ocean Blue</p>
                              <p className="text-xs text-gray-500">Trust & reliability</p>
                            </div>

                            {/* Forest Green Template */}
                            <div
                              className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${field.value === 'forest' ? 'border-green-500 bg-green-50' : 'border-gray-200 hover:border-green-300'
                                }`}
                              onClick={() => {
                                field.onChange('forest');
                                form.setValue('primaryColor', '#10B981');
                                form.setValue('secondaryColor', '#F0FDF4');
                              }}
                            >
                              <div className="flex items-center space-x-2 mb-2">
                                <div className="w-4 h-4 bg-green-500 rounded"></div>
                                <div className="w-4 h-4 bg-green-50 border rounded"></div>
                              </div>
                              <p className="font-medium text-sm">Forest Green</p>
                              <p className="text-xs text-gray-500">Natural & growth</p>
                            </div>

                            {/* Sunset Orange Template */}
                            <div
                              className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${field.value === 'sunset' ? 'border-orange-500 bg-orange-50' : 'border-gray-200 hover:border-orange-300'
                                }`}
                              onClick={() => {
                                field.onChange('sunset');
                                form.setValue('primaryColor', '#F59E0B');
                                form.setValue('secondaryColor', '#FFFBEB');
                              }}
                            >
                              <div className="flex items-center space-x-2 mb-2">
                                <div className="w-4 h-4 bg-orange-500 rounded"></div>
                                <div className="w-4 h-4 bg-orange-50 border rounded"></div>
                              </div>
                              <p className="font-medium text-sm">Sunset Orange</p>
                              <p className="text-xs text-gray-500">Energy & warmth</p>
                            </div>

                            {/* Elegant Dark Template */}
                            <div
                              className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${field.value === 'dark' ? 'border-gray-700 bg-gray-50' : 'border-gray-200 hover:border-gray-400'
                                }`}
                              onClick={() => {
                                field.onChange('dark');
                                form.setValue('primaryColor', '#374151');
                                form.setValue('secondaryColor', '#F9FAFB');
                              }}
                            >
                              <div className="flex items-center space-x-2 mb-2">
                                <div className="w-4 h-4 bg-gray-700 rounded"></div>
                                <div className="w-4 h-4 bg-gray-50 border rounded"></div>
                              </div>
                              <p className="font-medium text-sm">Elegant Dark</p>
                              <p className="text-xs text-gray-500">Sophisticated & modern</p>
                            </div>

                            {/* Cherry Red Template */}
                            <div
                              className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${field.value === 'cherry' ? 'border-red-500 bg-red-50' : 'border-gray-200 hover:border-red-300'
                                }`}
                              onClick={() => {
                                field.onChange('cherry');
                                form.setValue('primaryColor', '#EF4444');
                                form.setValue('secondaryColor', '#FEF2F2');
                              }}
                            >
                              <div className="flex items-center space-x-2 mb-2">
                                <div className="w-4 h-4 bg-red-500 rounded"></div>
                                <div className="w-4 h-4 bg-red-50 border rounded"></div>
                              </div>
                              <p className="font-medium text-sm">Cherry Red</p>
                              <p className="text-xs text-gray-500">Bold & attention-grabbing</p>
                            </div>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Show current colors for reference */}
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm font-medium mb-2">Current Colors:</p>
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <div
                          className="w-6 h-6 rounded border"
                          style={{ backgroundColor: form.watch('primaryColor') }}
                        ></div>
                        <span className="text-sm">Primary: {form.watch('primaryColor')}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div
                          className="w-6 h-6 rounded border"
                          style={{ backgroundColor: form.watch('secondaryColor') }}
                        ></div>
                        <span className="text-sm">Secondary: {form.watch('secondaryColor')}</span>
                      </div>
                    </div>
                  </div>

                  <FormField
                    control={form.control}
                    name="position"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Widget Position</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select position" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="bottom-right">Bottom Right</SelectItem>
                            <SelectItem value="bottom-left">Bottom Left</SelectItem>
                            <SelectItem value="top-right">Top Right</SelectItem>
                            <SelectItem value="top-left">Top Left</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="animation"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Animation</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="none">None</SelectItem>
                              <SelectItem value="fade">Fade</SelectItem>
                              <SelectItem value="slide">Slide</SelectItem>
                              <SelectItem value="bounce">Bounce</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="shadow"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Shadow</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="none">None</SelectItem>
                              <SelectItem value="sm">Small</SelectItem>
                              <SelectItem value="md">Medium</SelectItem>
                              <SelectItem value="lg">Large</SelectItem>
                              <SelectItem value="xl">Extra Large</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="fontSize"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Font Size</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="sm">Small</SelectItem>
                              <SelectItem value="md">Medium</SelectItem>
                              <SelectItem value="lg">Large</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="fontFamily"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Font Family</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="Inter">Inter</SelectItem>
                            <SelectItem value="Roboto">Roboto</SelectItem>
                            <SelectItem value="Open Sans">Open Sans</SelectItem>
                            <SelectItem value="Lato">Lato</SelectItem>
                            <SelectItem value="Poppins">Poppins</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              {/* Logo & Branding */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <FileText className="w-5 h-5 mr-2 text-orange-600" />
                    Logo & Branding
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <FileText className="w-5 h-5 text-orange-600" />
                      <div>
                        <p className="font-medium">Custom Logo</p>
                        <p className="text-sm text-muted-foreground">Upload your company logo</p>
                      </div>
                    </div>
                    <FormField
                      control={form.control}
                      name="logoUpload"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Switch checked={field.value} onCheckedChange={field.onChange} />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>

                  {form.watch('logoUpload') && (
                    <FormField
                      control={form.control}
                      name="logoUrl"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Logo URL</FormLabel>
                          <FormControl>
                            <Input placeholder="https://your-logo-url.com/logo.png" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  <div className="grid grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="buttonText"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Button Text</FormLabel>
                          <FormControl>
                            <Input placeholder="Chat with us" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="buttonSize"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Button Size</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="sm">Small</SelectItem>
                              <SelectItem value="md">Medium</SelectItem>
                              <SelectItem value="lg">Large</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="buttonShape"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Button Shape</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="round">Round</SelectItem>
                              <SelectItem value="square">Square</SelectItem>
                              <SelectItem value="pill">Pill</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Features - Simplified with inline configuration */}
              <Collapsible open={featuresOpen} onOpenChange={setFeaturesOpen}>
                <Card>
                  <CardHeader>
                    <CollapsibleTrigger asChild>
                      <div className="flex items-center justify-between cursor-pointer">
                        <CardTitle className="flex items-center text-lg">
                          <Settings className="w-5 h-5 mr-2 text-green-600" />
                          Features
                        </CardTitle>
                        {featuresOpen ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                      </div>
                    </CollapsibleTrigger>
                  </CardHeader>
                  <CollapsibleContent>
                    <CardContent className="space-y-4">
                      {/* Pre-Chat Form */}
                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <FileText className="w-5 h-5 text-blue-600" />
                          <div>
                            <p className="font-medium">Pre-Chat Form</p>
                            <p className="text-sm text-muted-foreground">Collect user info before chat starts</p>
                          </div>
                        </div>
                        <FormField
                          control={form.control}
                          name="preChat"
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Switch checked={field.value} onCheckedChange={field.onChange} />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Webhooks */}
                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <Link className="w-5 h-5 text-purple-600" />
                          <div>
                            <p className="font-medium">Webhooks</p>
                            <p className="text-sm text-muted-foreground">Send chat data to external services</p>
                          </div>
                        </div>
                        <FormField
                          control={form.control}
                          name="webhooks"
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Switch checked={field.value} onCheckedChange={field.onChange} />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Webhook Configuration - shown when webhooks enabled */}
                      {form.watch('webhooks') && (
                        <div className="space-y-4 p-4 border rounded-lg bg-purple-50">
                          <FormField
                            control={form.control}
                            name="webhookUrl"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Webhook URL</FormLabel>
                                <FormControl>
                                  <Input placeholder="https://your-webhook-url.com" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="webhookSecret"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Webhook Secret (Optional)</FormLabel>
                                <FormControl>
                                  <Input placeholder="Your webhook secret key" type="password" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      )}

                      {/* Post-Chat Survey */}
                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <CheckCircle2 className="w-5 h-5 text-green-600" />
                          <div>
                            <p className="font-medium">Post-Chat Survey</p>
                            <p className="text-sm text-muted-foreground">Collect feedback after conversations</p>
                          </div>
                        </div>
                        <FormField
                          control={form.control}
                          name="postChat"
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Switch checked={field.value} onCheckedChange={field.onChange} />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Conversation Persistence */}
                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <Shield className="w-5 h-5 text-blue-600" />
                          <div>
                            <p className="font-medium">Conversation Persistence</p>
                            <p className="text-sm text-muted-foreground">Save conversations across sessions</p>
                          </div>
                        </div>
                        <FormField
                          control={form.control}
                          name="conversationPersistence"
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Switch checked={field.value} onCheckedChange={field.onChange} />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Custom CSS */}
                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <FileText className="w-5 h-5 text-purple-600" />
                          <div>
                            <p className="font-medium">Custom CSS</p>
                            <p className="text-sm text-muted-foreground">Add custom styling</p>
                          </div>
                        </div>
                        <FormField
                          control={form.control}
                          name="customCSS"
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Switch checked={field.value} onCheckedChange={field.onChange} />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Custom CSS Code - shown when custom CSS enabled */}
                      {form.watch('customCSS') && (
                        <FormField
                          control={form.control}
                          name="customCSSCode"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Custom CSS Code</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="/* Your custom CSS here */"
                                  className="min-h-[120px] font-mono"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}

                      {/* Domain Restriction */}
                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <Shield className="w-5 h-5 text-orange-600" />
                          <div>
                            <p className="font-medium">Domain Restriction</p>
                            <p className="text-sm text-muted-foreground">Limit widget to specific domains</p>
                          </div>
                        </div>
                        <FormField
                          control={form.control}
                          name="domainRestriction"
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Switch checked={field.value} onCheckedChange={field.onChange} />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Mobile Optimization */}
                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <Smartphone className="w-5 h-5 text-blue-600" />
                          <div>
                            <p className="font-medium">Mobile Optimization</p>
                            <p className="text-sm text-muted-foreground">Optimize for mobile devices</p>
                          </div>
                        </div>
                        <FormField
                          control={form.control}
                          name="mobileOptimization"
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Switch checked={field.value} onCheckedChange={field.onChange} />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* AI Model Selection */}
                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <Brain className="w-5 h-5 text-purple-600" />
                          <div>
                            <p className="font-medium">AI Model Selection</p>
                            <p className="text-sm text-muted-foreground">Allow users to choose AI model</p>
                          </div>
                        </div>
                        <FormField
                          control={form.control}
                          name="aiModelSelection"
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Switch checked={field.value} onCheckedChange={field.onChange} />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* AI Model Configuration - shown when AI model selection enabled */}
                      {form.watch('aiModelSelection') && (
                        <div className="space-y-4 p-4 border rounded-lg bg-purple-50">
                          <FormField
                            control={form.control}
                            name="selectedAiModel"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Default AI Model</FormLabel>
                                <Select onValueChange={field.onChange} defaultValue={field.value}>
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select AI model" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="auto">Auto (Recommended)</SelectItem>
                                    <SelectItem value="gpt-4">GPT-4</SelectItem>
                                    <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                                    <SelectItem value="claude-3">Claude 3</SelectItem>
                                    <SelectItem value="gemini">Gemini</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <div className="grid grid-cols-2 gap-4">
                            <FormField
                              control={form.control}
                              name="contextRetention"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Context Retention</FormLabel>
                                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                                    <FormControl>
                                      <SelectTrigger>
                                        <SelectValue />
                                      </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                      <SelectItem value="session">Session Only</SelectItem>
                                      <SelectItem value="persistent">Persistent</SelectItem>
                                      <SelectItem value="none">None</SelectItem>
                                    </SelectContent>
                                  </Select>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={form.control}
                              name="maxMessagesStored"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Max Messages Stored</FormLabel>
                                  <FormControl>
                                    <Input
                                      type="number"
                                      min="10"
                                      max="1000"
                                      {...field}
                                      onChange={(e) => field.onChange(Number(e.target.value))}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </CollapsibleContent>
                </Card>
              </Collapsible>

              {/* Behavior Settings - Collapsible Advanced Section */}
              <Collapsible open={advancedOpen} onOpenChange={setAdvancedOpen}>
                <Card>
                  <CardHeader>
                    <CollapsibleTrigger asChild>
                      <div className="flex items-center justify-between cursor-pointer">
                        <CardTitle className="flex items-center text-lg">
                          <Settings className="w-5 h-5 mr-2 text-gray-600" />
                          Behavior Settings
                        </CardTitle>
                        {advancedOpen ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                      </div>
                    </CollapsibleTrigger>
                  </CardHeader>
                  <CollapsibleContent>
                    <CardContent className="space-y-4">
                      {/* Auto Open */}
                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <CheckCircle2 className="w-5 h-5 text-green-600" />
                          <div>
                            <p className="font-medium">Auto Open</p>
                            <p className="text-sm text-muted-foreground">Automatically open widget on page load</p>
                          </div>
                        </div>
                        <FormField
                          control={form.control}
                          name="autoOpen"
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Switch checked={field.value} onCheckedChange={field.onChange} />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Auto Open Delay - shown when auto open enabled */}
                      {form.watch('autoOpen') && (
                        <FormField
                          control={form.control}
                          name="autoOpenDelay"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Auto Open Delay (seconds)</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="0"
                                  max="60"
                                  {...field}
                                  onChange={(e) => field.onChange(Number(e.target.value))}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}

                      {/* Show Typing Indicator */}
                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <MessageSquare className="w-5 h-5 text-blue-600" />
                          <div>
                            <p className="font-medium">Typing Indicator</p>
                            <p className="text-sm text-muted-foreground">Show when AI is typing</p>
                          </div>
                        </div>
                        <FormField
                          control={form.control}
                          name="showTypingIndicator"
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Switch checked={field.value} onCheckedChange={field.onChange} />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Enable User Ratings */}
                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <CheckCircle2 className="w-5 h-5 text-yellow-600" />
                          <div>
                            <p className="font-medium">User Ratings</p>
                            <p className="text-sm text-muted-foreground">Allow users to rate responses</p>
                          </div>
                        </div>
                        <FormField
                          control={form.control}
                          name="enableUserRatings"
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Switch checked={field.value} onCheckedChange={field.onChange} />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Start Minimized */}
                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <MessageSquare className="w-5 h-5 text-gray-600" />
                          <div>
                            <p className="font-medium">Start Minimized</p>
                            <p className="text-sm text-muted-foreground">Widget starts in minimized state</p>
                          </div>
                        </div>
                        <FormField
                          control={form.control}
                          name="startMinimized"
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Switch checked={field.value} onCheckedChange={field.onChange} />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Close After Inactivity */}
                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <AlertTriangle className="w-5 h-5 text-orange-600" />
                          <div>
                            <p className="font-medium">Close After Inactivity</p>
                            <p className="text-sm text-muted-foreground">Auto-close widget after period of inactivity</p>
                          </div>
                        </div>
                        <FormField
                          control={form.control}
                          name="closeAfterInactivity"
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Switch checked={field.value} onCheckedChange={field.onChange} />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Inactivity Timeout - shown when close after inactivity enabled */}
                      {form.watch('closeAfterInactivity') && (
                        <FormField
                          control={form.control}
                          name="inactivityTimeout"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Inactivity Timeout (minutes)</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="1"
                                  max="60"
                                  {...field}
                                  onChange={(e) => field.onChange(Number(e.target.value))}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}

                      {/* Collect User Data */}
                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <Shield className="w-5 h-5 text-blue-600" />
                          <div>
                            <p className="font-medium">Collect User Data</p>
                            <p className="text-sm text-muted-foreground">Store user interactions and analytics</p>
                          </div>
                        </div>
                        <FormField
                          control={form.control}
                          name="collectUserData"
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Switch checked={field.value} onCheckedChange={field.onChange} />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Enable Analytics */}
                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <CheckCircle2 className="w-5 h-5 text-green-600" />
                          <div>
                            <p className="font-medium">Enable Analytics</p>
                            <p className="text-sm text-muted-foreground">Track widget performance and usage</p>
                          </div>
                        </div>
                        <FormField
                          control={form.control}
                          name="enableAnalytics"
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Switch checked={field.value} onCheckedChange={field.onChange} />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Debug Mode */}
                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <AlertTriangle className="w-5 h-5 text-red-600" />
                          <div>
                            <p className="font-medium">Debug Mode</p>
                            <p className="text-sm text-muted-foreground">Enable debugging for development</p>
                          </div>
                        </div>
                        <FormField
                          control={form.control}
                          name="debugMode"
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Switch checked={field.value} onCheckedChange={field.onChange} />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Load Timeout */}
                      <FormField
                        control={form.control}
                        name="loadTimeoutMs"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Load Timeout (milliseconds)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="1000"
                                max="30000"
                                {...field}
                                onChange={(e) => field.onChange(Number(e.target.value))}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                  </CollapsibleContent>
                </Card>
              </Collapsible>

              {/* Mobile Settings - New Advanced Section */}
              <Collapsible>
                <Card>
                  <CardHeader>
                    <CollapsibleTrigger asChild>
                      <div className="flex items-center justify-between cursor-pointer">
                        <CardTitle className="flex items-center text-lg">
                          <Smartphone className="w-5 h-5 mr-2 text-blue-600" />
                          Mobile Settings
                        </CardTitle>
                        <ChevronDown className="w-4 h-4" />
                      </div>
                    </CollapsibleTrigger>
                  </CardHeader>
                  <CollapsibleContent>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="mobileBreakpoint"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Mobile Breakpoint (px)</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="320"
                                  max="1024"
                                  {...field}
                                  onChange={(e) => field.onChange(Number(e.target.value))}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="mobilePosition"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Mobile Position</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="bottom">Bottom</SelectItem>
                                  <SelectItem value="top">Top</SelectItem>
                                  <SelectItem value="full">Full Screen</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={form.control}
                        name="mobileAnimation"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Mobile Animation</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="slide-up">Slide Up</SelectItem>
                                <SelectItem value="fade">Fade</SelectItem>
                                <SelectItem value="none">None</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                  </CollapsibleContent>
                </Card>
              </Collapsible>

              {/* Validation Summary */}
              {Object.keys(form.formState.errors).length > 0 && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Please fix {Object.keys(form.formState.errors).length} error(s) before saving.
                  </AlertDescription>
                </Alert>
              )}
            </Form>
          </div>
        </div>
      </div>
    </div>
  );
};

// Main component - simplified without theme provider
const SmartWidgetBuilder = (props: SimpleWidgetBuilderProps) => {
  return <SimpleWidgetBuilderContent {...props} />;
};

export default SmartWidgetBuilder;