import { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { authService } from '@/utils/api-service';
import type { User } from '@/utils/authService';

// Define the type for our context
interface AuthContextType {
  user: User | null;
  login: (email: string, password: string, rememberMe?: boolean) => Promise<void>;
  logout: () => Promise<void>;
  isLoading: boolean;
}

// Create the context with a default value
const AuthContext = createContext<AuthContextType>({
  user: null,
  login: async () => { },
  logout: async () => { },
  isLoading: false,
});

// Create a provider component for the auth context
export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(() => {
    // Try to restore user from localStorage as fallback
    try {
      const savedUser = localStorage.getItem('auth_user');
      return savedUser ? JSON.parse(savedUser) : null;
    } catch {
      return null;
    }
  });
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Check auth status when the component mounts
  useEffect(() => {
    const checkAuthStatus = async () => {
      setIsLoading(true);
      try {
        const isAuthenticated = await authService.checkAuth();

        if (isAuthenticated) {
          const currentUser = await authService.getCurrentUser();
          setUser(currentUser);
          // Save to localStorage as backup
          localStorage.setItem('auth_user', JSON.stringify(currentUser));
        } else {
          setUser(null);
          // Clear localStorage
          localStorage.removeItem('auth_user');
        }
      } catch (error) {
        console.error('Error checking auth status:', error);
        // Clear user state on error
        setUser(null);
        // Clear localStorage
        localStorage.removeItem('auth_user');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthStatus();
  }, []);

  const login = async (email: string, password: string, rememberMe = false) => {
    setIsLoading(true);
    try {
      const user = await authService.login(email, password, rememberMe);
      setUser(user);
      // Save to localStorage as backup
      localStorage.setItem('auth_user', JSON.stringify(user));
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    setIsLoading(true);
    try {
      await authService.logout();
      setUser(null);
      // Clear localStorage
      localStorage.removeItem('auth_user');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthContext.Provider value={{ user, login, logout, isLoading }}>
      {children}
    </AuthContext.Provider>
  );
};

// Create a custom hook to use the auth context
export const useAuth = () => useContext(AuthContext);
